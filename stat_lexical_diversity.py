#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
词汇多样性分析工具 (Lexical Diversity Analysis Tool)

此脚本用于分析对话数据中的词汇多样性，包括余弦相似度分析等功能。

主要功能:
    1. 余弦相似度分析 - 使用TF-IDF和余弦相似度分析文本相似度
    2. 最长公共子序列(LCS)分析 - 分析文本间的最长公共子序列

用法:
    python stat_lexical_diversity.py input_file [--output_dir OUTPUT_DIR]

参数:
    input_file            输入的JSON文件路径，包含OpenAI格式的对话数据
    --output_dir, -o      输出文件的目录路径 (默认: ./output)
"""

import json
import re
import numpy as np
import os
import argparse
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

def load_data(file_path):
    """加载JSON数据文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_conversations(data):
    """从OpenAI格式的数据中提取对话信息"""
    conversations = []
    skipped_count = 0

    for idx, item in enumerate(data):
        if 'messages' not in item:
            skipped_count += 1
            continue

        conversation = {
            'id': item.get('persona_id', f'conversation_{idx}'),
            'messages': item['messages'],
            'user_messages': [],
            'assistant_messages': []
        }

        for msg in item['messages']:
            if msg.get('role') == 'user':
                content = msg.get('content', '').strip()
                if content:
                    conversation['user_messages'].append(content)
            elif msg.get('role') == 'assistant':
                content = msg.get('content', '').strip()
                if content:
                    conversation['assistant_messages'].append(content)

        user_msg_count = len(conversation['user_messages'])
        assistant_msg_count = len(conversation['assistant_messages'])
        conversation['turns'] = min(user_msg_count, assistant_msg_count)

        if user_msg_count > assistant_msg_count:
            conversation['turns'] += 1

        if conversation['turns'] > 0:
            conversations.append(conversation)
        else:
            skipped_count += 1

    if skipped_count > 0:
        print(f"跳过了 {skipped_count} 条无效对话")

    return conversations

def is_table_content(content):
    """
    检查内容是否包含表格标记

    参数:
        content: 要检查的文本内容

    返回:
        bool: 如果包含表格标记则返回True
    """
    return "|-----" in content or "|----" in content

def cosine_analyze_similarity_by_role(conversations, role_filter=None):
    """
    使用TF-IDF和余弦相似度分析指定角色的文本相似度，找出最相似的文本对

    参数:
        conversations: 对话列表
        role_filter: 角色过滤器，可以是'user', 'assistant'或None(全部)

    返回:
        json_results: 相似度高于阈值的文本对列表（JSON格式）
    """
    # 收集指定角色的非空回复，排除表格内容
    all_messages = []
    message_sources = []  # 记录每个消息的来源（会话ID和角色）

    for i, dialogue in enumerate(conversations):
        conv_id = dialogue.get('id', f"conv_{i}")
        messages = dialogue['messages']

        for msg in messages:
            # 根据role_filter过滤消息
            if role_filter is not None and msg['role'] != role_filter:
                continue

            if msg['content'].strip() and len(msg['content'].strip()) > 10:
                content = msg['content'].strip()

                # 排除包含表格标记的内容
                if is_table_content(content):
                    continue

                all_messages.append(content)
                message_sources.append((conv_id, msg['role']))

    role_name = role_filter if role_filter else "全部"
    print(f"余弦相似度分析({role_name}): 共收集 {len(all_messages)} 条有效消息进行相似度分析")

    # 如果消息数量太少，无法进行分析
    if len(all_messages) < 2:
        return []

    # 使用TF-IDF向量化文本
    vectorizer = TfidfVectorizer(analyzer='char', ngram_range=(2, 3))
    tfidf_matrix = vectorizer.fit_transform(all_messages)

    # 计算余弦相似度矩阵
    cosine_sim = cosine_similarity(tfidf_matrix)

    # 找出相似度最高的对（不包括自身）
    similar_pairs = []
    n = len(all_messages)
    similarity_threshold = 0.6  # 相似度阈值

    for i in range(n):
        for j in range(i+1, n):  # 只比较不同索引，避免自身比较
            # 排除完全相同的文本
            if all_messages[i] == all_messages[j]:
                continue

            similarity = cosine_sim[i, j]
            if similarity > similarity_threshold:  # 仅考虑相似度超过阈值的对
                similar_pairs.append((similarity, all_messages[i], all_messages[j], i, j, message_sources[i], message_sources[j]))

    # 按相似度降序排序
    similar_pairs.sort(reverse=True, key=lambda x: x[0])

    # 将结果转换为JSON友好的格式
    json_results = []
    for similarity, msg1, msg2, idx1, idx2, source1, source2 in similar_pairs:
        json_results.append({
            "similarity": similarity,
            "message1": msg1,
            "message2": msg2,
            "index1": idx1,
            "index2": idx2,
            "source1": source1[0],  # 会话ID
            "role1": source1[1],    # 角色
            "source2": source2[0],  # 会话ID
            "role2": source2[1],    # 角色
            "is_same_conversation": (source1[0] == source2[0]),
            "is_same_role": (source1[1] == source2[1])
        })

    return json_results

def analyze_cosine_similarity(conversations, output_dir, output_base):
    """
    执行余弦相似度分析并保存结果

    参数:
        conversations: 对话列表
        output_dir: 输出目录
        output_base: 输出文件基础名称
    """
    print("正在进行余弦相似度分析...")

    # 分析助手回复的相似度
    print("  - 分析助手回复相似度...")
    assistant_results = cosine_analyze_similarity_by_role(conversations, role_filter='assistant')

    # 分析用户消息的相似度
    print("  - 分析用户消息相似度...")
    user_results = cosine_analyze_similarity_by_role(conversations, role_filter='user')

    # 分析全部消息的相似度
    print("  - 分析全部消息相似度...")
    all_results = cosine_analyze_similarity_by_role(conversations, role_filter=None)

    # 保存助手回复相似度结果
    if assistant_results:
        for i, item in enumerate(assistant_results, 1):
            item['rank'] = i
        assistant_output_file = os.path.join(output_dir, f"{output_base}_cosine_similarity_assistant.json")
        with open(assistant_output_file, 'w', encoding='utf-8') as f:
            json.dump(assistant_results, f, ensure_ascii=False, indent=4)
        print(f"助手回复相似度: 共找到 {len(assistant_results)} 对相似度大于0.6的回复，已保存到 {assistant_output_file}")
    else:
        print("助手回复相似度: 未找到相似度大于0.6的回复对")

    # 保存用户消息相似度结果
    if user_results:
        for i, item in enumerate(user_results, 1):
            item['rank'] = i
        user_output_file = os.path.join(output_dir, f"{output_base}_cosine_similarity_user.json")
        with open(user_output_file, 'w', encoding='utf-8') as f:
            json.dump(user_results, f, ensure_ascii=False, indent=4)
        print(f"用户消息相似度: 共找到 {len(user_results)} 对相似度大于0.6的消息，已保存到 {user_output_file}")
    else:
        print("用户消息相似度: 未找到相似度大于0.6的消息对")

    # 保存全部消息相似度结果
    if all_results:
        for i, item in enumerate(all_results, 1):
            item['rank'] = i
        all_output_file = os.path.join(output_dir, f"{output_base}_cosine_similarity_all.json")
        with open(all_output_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=4)
        print(f"全部消息相似度: 共找到 {len(all_results)} 对相似度大于0.6的消息，已保存到 {all_output_file}")

        # 输出前10个最相似的结果摘要（仅显示全部消息的结果）
        print("\n全部消息中相似度最高的前10对:")
        print("=" * 80)
        for i, item in enumerate(all_results[:10], 1):
            similarity = item.get('similarity', 0)
            role1 = item.get('role1', 'N/A')
            role2 = item.get('role2', 'N/A')
            is_same_conv = item.get('is_same_conversation', False)
            is_same_role = item.get('is_same_role', False)

            same_conv_text = "同一对话" if is_same_conv else "不同对话"
            same_role_text = "同角色" if is_same_role else "不同角色"

            print(f"{i:2d}. 相似度: {similarity:.4f} | {same_conv_text} | {same_role_text} | {role1}-{role2}")

            # 显示消息内容的前50个字符
            msg1 = item.get('message1', '')[:50] + "..." if len(item.get('message1', '')) > 50 else item.get('message1', '')
            msg2 = item.get('message2', '')[:50] + "..." if len(item.get('message2', '')) > 50 else item.get('message2', '')
            print(f"    消息1({role1}): {msg1}")
            print(f"    消息2({role2}): {msg2}")
            print()
    else:
        print("全部消息相似度: 未找到相似度大于0.6的消息对")

def main():
    parser = argparse.ArgumentParser(description="词汇多样性分析工具")
    parser.add_argument("input_file", help="输入的JSON文件路径")
    parser.add_argument("--output_dir", "-o", default="./output", help="输出文件的目录路径")
    args = parser.parse_args()

    os.makedirs(args.output_dir, exist_ok=True)

    print("正在加载数据...")
    data = load_data(args.input_file)
    conversations = extract_conversations(data)

    output_base = os.path.basename(args.input_file).split('.')[0]

    # 执行余弦相似度分析
    analyze_cosine_similarity(conversations, args.output_dir, output_base)

    print(f"\n词汇多样性分析完成！所有结果已保存到 {args.output_dir} 目录")

if __name__ == "__main__":
    main()
